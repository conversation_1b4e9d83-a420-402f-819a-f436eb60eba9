[versions]
agp = "8.12.1"
kotlin = "2.0.21"
coreKtx = "1.17.0"
leanback = "1.2.0"
glide = "4.11.0"
composeBom = "2025.08.00"
activityCompose = "1.10.1"
tvMaterial = "1.0.0"
tvFoundation = "1.0.0-alpha09"

[libraries]
androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
androidx-leanback = { group = "androidx.leanback", name = "leanback", version.ref = "leanback" }
glide = { group = "com.github.bumptech.glide", name = "glide", version.ref = "glide" }

# Compose BOM
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }

# Compose Core
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-compose-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-compose-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-compose-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }

# Compose for TV
androidx-tv-material = { group = "androidx.tv", name = "tv-material", version.ref = "tvMaterial" }
androidx-tv-foundation = { group = "androidx.tv", name = "tv-foundation", version.ref = "tvFoundation" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }

